import os
from django.http import HttpResponse, HttpResponseRedirect, StreamingHttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from accounts.serializers import  ClientSerializer, FolioSerializer, ClientSearchSerializer, DocumentUploadSerializer, StaffDocumentManagerSerializer
from accounts.models import Folio, User, Client, Document
from accounts.permissions import IsStaff
from admin_portal.views import SearchClientData
from rest_framework.permissions import IsAuthenticated
# from django.shortcuts import render
from rest_framework.parsers import MultiPartParser, FormParser
from datetime import date
from django.db.models import Count
from accounts.mail import send_mail
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from client.performance_services import get_overall_fund_performance_chart_data

class StaffDashboardView(APIView):
   permission_classes = [IsAuthenticated, IsStaff]
   def get(self, request):
       return Response({"message": f"Welcome, Staff {request.user.username}"})

class StaffDashboardStatsView(APIView):
    permission_classes = [IsAuthenticated, IsStaff]

    def get(self, request):
        num_clients = Client.objects.count()
        num_active_folios = Folio.objects.filter(terminated=False).count()

        # Get real performance data from the database
        try:
            performance_data = get_overall_fund_performance_chart_data()

            # Format the data for the chart
            fund_vs_benchmark = {
                "labels": [item["period"] for item in performance_data],
                "fund_performance": [item["fund_performance"] for item in performance_data],
                "benchmark_performance": [item["benchmark_performance"] for item in performance_data]
            }
        except Exception as e:
            # Fallback to empty data if performance data is not available
            fund_vs_benchmark = {
                "labels": [],
                "fund_performance": [],
                "benchmark_performance": []
            }

        data = {
            "num_clients": num_clients,
            "num_active_folios": num_active_folios,
            "fund_vs_benchmark": fund_vs_benchmark
        }

        return Response(data, status=status.HTTP_200_OK)

@method_decorator(ratelimit(key='user_or_ip', rate='10/m', block=True), name='dispatch')
class RegisterClient(APIView):
    permission_classes = [IsAuthenticated, IsStaff]  # Re-enabled security!

    def post(self, request):
        serializer = ClientSerializer(data=request.data, context={'request': request})
        if serializer.is_valid(raise_exception=True):
                client_instance = serializer.save()
                user = client_instance.user
                send_mail('welcome', user)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
@method_decorator(ratelimit(key='user_or_ip', rate='10/m', block=True), name='dispatch')
class FolioMethods(APIView):
    permission_classes = [IsAuthenticated, IsStaff] # Example permission

    def post(self, request):
        client_username = request.data.get('client_username')
        folio_number = request.data.get('folio_number')

        if not client_username:
            return Response({"detail": "Client username is required."}, status=status.HTTP_400_BAD_REQUEST)

        if not folio_number:
            return Response({"detail": "Folio number is required."}, status=status.HTTP_400_BAD_REQUEST)

        # Validate folio number
        try:
            folio_number = int(folio_number)
            if folio_number <= 0:
                return Response({"detail": "Folio number must be a positive integer."}, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError):
            return Response({"detail": "Folio number must be a valid integer."}, status=status.HTTP_400_BAD_REQUEST)

        # Check if folio number already exists
        if Folio.objects.filter(folio_number=folio_number).exists():
            return Response({"detail": "This folio number already exists."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Find the User first, then the associated Client
            user = User.objects.get(username__iexact=client_username, role='client')
            client = Client.objects.get(user=user)

        except User.DoesNotExist:
            return Response({"detail": "Client with this username not found."}, status=status.HTTP_404_NOT_FOUND)

        except Client.DoesNotExist:
            # This case should ideally not happen if a User with role='client' exists
            # but it's good for robustness.
            return Response({"detail": "Client profile not found for this user."}, status=status.HTTP_404_NOT_FOUND)

        # Create a new Folio for the found client with the specified folio number
        new_folio = Folio.objects.create(client=client, folio_number=folio_number, terminated=False)

        serializer = FolioSerializer(new_folio) # Serialize the newly created folio
        return Response({"message": f"New folio {folio_number} created successfully.", "folio": serializer.data}, status=status.HTTP_201_CREATED)
   
    def delete(self, request):
        folio_number = request.data.get('folio_number')


        if not folio_number:
            return Response({"detail": "Folio number is required."}, status=status.HTTP_400_BAD_REQUEST)
        try:
            folio = Folio.objects.get(folio_number=folio_number)
        except Folio.DoesNotExist:
            return Response({"detail": "Folio not found."}, status=status.HTTP_404_NOT_FOUND)

        if folio.terminated:
            return Response({"detail": "Folio is already terminated."}, status=status.HTTP_200_OK)
        folio.terminated = True
        folio.save()
        
        serializer = FolioSerializer(folio) # Serialize the updated folio
        return Response({"message": f"Folio {folio_number} terminated successfully."}, status=status.HTTP_200_OK)  

class SearchClientDataForStaff(SearchClientData):

    permission_classes = [IsAuthenticated, IsStaff]  # Ensure only staff can access this view

    def get(self, request):
        return super().get(request)  # Call the parent class's get method to handle the search logic
    
@method_decorator(ratelimit(key='user_or_ip', rate='15/m', block=True), name='dispatch')
class FileUploadView(APIView):
    permission_classes = [IsAuthenticated, IsStaff] 
    parser_classes = [MultiPartParser, FormParser]

    def get(self, request, *args, **kwargs):
        """
        Checks if a document already exists based on query parameters.
        (This method remains unchanged as its logic is sound for checking existence).
        """
        params = request.query_params
        doc_type = params.get('document_type') 
        year = params.get('year', date.today().year)
        month = params.get('month', date.today().month)

        if not doc_type:
            return Response({"detail": "document_type is required."}, status=status.HTTP_400_BAD_REQUEST)

        lookup_filters = {'document_type': doc_type, 'year': year, 'month': month}

        if doc_type == 'Company':
            subtype = params.get('subtype')
            if not subtype:
                return Response({"detail": "subtype is required for Company documents."}, status=status.HTTP_400_BAD_REQUEST)
            lookup_filters['subtype'] = subtype
        elif doc_type == 'Client':
            folio_number = params.get('folio_number')
            if not folio_number:
                return Response({"detail": "folio_number is required for Client documents."}, status=status.HTTP_400_BAD_REQUEST)
            lookup_filters['folio__folio_number'] = folio_number
        else:
            return Response({"detail": "Invalid document_type."}, status=status.HTTP_400_BAD_REQUEST)

        if Document.objects.filter(**lookup_filters).exists():
            return Response({"exists": True, "detail": "A file of this type for this period already exists."}, status=200)
        
        return Response({"exists": False, "detail": "No existing file found. You can proceed."}, status=404)


    def post(self, request, *args, **kwargs):
        """
        Handles the file upload and triggers the serializer to perform
        validation and database updates.
        """
        # The view's job is now simple: pass the data to the serializer.
        serializer = DocumentUploadSerializer(data=request.data)
        
        if serializer.is_valid():
            # The .save() method will now trigger our custom .create() method
            # in the serializer, which handles all the complex logic.
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        # If validation fails, the detailed errors from the serializer are returned.
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, *args, **kwargs):
        """
        Deletes a document based on query parameters.
        """
        params = request.query_params
        doc_type = params.get('document_type') 
        year = params.get('year', date.today().year)
        month = params.get('month', date.today().month)

        if not doc_type:
            return Response({"detail": "document_type is required."}, status=status.HTTP_400_BAD_REQUEST)

        lookup_filters = {'document_type': doc_type, 'year': year, 'month': month}

        if doc_type == 'Company':
            subtype = params.get('subtype')
            if not subtype:
                return Response({"detail": "subtype is required for Company documents."}, status=status.HTTP_400_BAD_REQUEST)
            lookup_filters['subtype'] = subtype
        elif doc_type == 'Client':
            folio_number = params.get('folio_number')
            if not folio_number:
                return Response({"detail": "folio_number is required for Client documents."}, status=status.HTTP_400_BAD_REQUEST)
            lookup_filters['folio__folio_number'] = folio_number
        else:
            return Response({"detail": "Invalid document_type."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            document = Document.objects.get(**lookup_filters)
            document.delete()
            return Response({"detail": "Document deleted successfully."}, status=status.HTTP_200_OK)
        except Document.DoesNotExist:
            return Response({"detail": "Document not found."}, status=status.HTTP_404_NOT_FOUND)

@method_decorator(ratelimit(key='user_or_ip', rate='30/m', block=True), name='dispatch')
class DownloadDocumentView(APIView):
    permission_classes = [IsAuthenticated, IsStaff]

    def get(self, request, doc_id):
        try:
            document = Document.objects.get(id=doc_id)
        except Document.DoesNotExist:
            return Response({"detail": "Document not found."}, status=status.HTTP_404_NOT_FOUND)

        # --- Logic to generate a descriptive filename ---
        new_filename = os.path.basename(document.uploaded_file.name)

        # Open the file from S3 storage (or other storage) in binary read mode
        file_iterator = document.uploaded_file.open('rb')
        
        # Create a memory-efficient streaming response
        response = StreamingHttpResponse(file_iterator, content_type='application/octet-stream')
        
        # Set headers to prompt download with the NEW, descriptive filename
        response['Content-Disposition'] = f'attachment; filename="{new_filename}"'
        response['Content-Length'] = document.uploaded_file.size
        
        return response

    
    # def get(self, request, *args, **kwargs):
    #     """
    #     Finds a document based on query parameters and redirects to its S3 URL.
    #     """
    #     params = request.query_params
    #     doc_type = params.get('document_type')
    #     year = params.get('year')
    #     month = params.get('month')

    #     # --- 1. Validate Parameters ---
    #     if not all([doc_type, year, month]):
    #         return Response(
    #             {"detail": "document_type, year, and month are required parameters."},
    #             status=status.HTTP_400_BAD_REQUEST
    #         )

    #     # --- 2. Build the Database Filter ---
    #     lookup_filters = {
    #         'document_type': doc_type,
    #         'year': year,
    #         'month': month,
    #     }

    #     if doc_type == 'Company':
    #         subtype = params.get('subtype')
    #         if not subtype:
    #             return Response({"detail": "A 'subtype' is required for Company documents."}, status=status.HTTP_400_BAD_REQUEST)
    #         lookup_filters['subtype'] = subtype

    #     elif doc_type == 'Client':
    #         folio_number = params.get('folio_number')
    #         if not folio_number:
    #             return Response({"detail": "A 'folio_number' is required for Client documents."}, status=status.HTTP_400_BAD_REQUEST)
    #         lookup_filters['folio__folio_number'] = folio_number
        
    #     else:
    #         return Response({"detail": "Invalid 'document_type'."}, status=status.HTTP_400_BAD_REQUEST)

    #     # --- 3. Find the Document and Stream ---
    #     try:
    #         document = Document.objects.get(**lookup_filters)
            
    #         # --- ✨ NEW: Logic to generate a descriptive filename ---
            
    #         # Get the file extension (e.g., .pdf, .xlsx) from the original file
    #         _, file_extension = os.path.splitext(document.uploaded_file.name)
            
    #         # Build the filename parts based on the document type
    #         filename_parts = [doc_type]
    #         if doc_type == 'Company':
    #             filename_parts.append(subtype)
    #         elif doc_type == 'Client':
    #             filename_parts.pop()
    #             filename_parts.append('Folio')
    #             filename_parts.append(folio_number)
            
    #         filename_parts.extend([year, month])
            
    #         # Join all parts with an underscore and add the extension
    #         new_filename = f"{'_'.join(filename_parts)}{file_extension}"

    #         # --- End of new logic ---

    #         # Open the file from S3 storage (or other storage) in binary read mode
    #         file_iterator = document.uploaded_file.open('rb')
            
    #         # Create a memory-efficient streaming response
    #         response = StreamingHttpResponse(file_iterator, content_type='application/octet-stream')
            
    #         # Set headers to prompt download with the NEW, descriptive filename
    #         response['Content-Disposition'] = f'attachment; filename="{new_filename}"'
    #         response['Content-Length'] = document.uploaded_file.size
            
    #         return response

    #     except Document.DoesNotExist:
    #         return Response({"detail": "Document not found with the specified criteria."}, status=status.HTTP_404_NOT_FOUND)
    #     except Exception as e:
    #         return Response({"detail": f"An error occurred: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StaffDocumentManagerView(APIView):
    """
    Staff document manager endpoint that returns all documents organized by type.
    Takes year and month as optional query parameters to filter results.
    """
    permission_classes = [IsAuthenticated, IsStaff]

    def get(self, request, *args, **kwargs):
        # Get optional year and month filters
        year_filter = getattr(request, 'query_params', request.GET).get('year')
        month_filter = getattr(request, 'query_params', request.GET).get('month')
        
        # Build base queryset
        documents_queryset = Document.objects.select_related('folio__client__user').all()
        
        # Apply filters if provided
        if year_filter:
            try:
                year_filter = int(year_filter)
                documents_queryset = documents_queryset.filter(year=year_filter)
            except ValueError:
                return Response({"detail": "Invalid year format."}, status=status.HTTP_400_BAD_REQUEST)
        
        if month_filter:
            try:
                month_filter = int(month_filter)
                if not (1 <= month_filter <= 12):
                    raise ValueError("Month must be between 1 and 12")
                documents_queryset = documents_queryset.filter(month=month_filter)
            except ValueError:
                return Response({"detail": "Invalid month format. Must be between 1 and 12."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Order by creation date (newest first)
        documents_queryset = documents_queryset.order_by('-created_at')
        
        # Separate company and client documents
        company_documents = documents_queryset.filter(document_type='Company')
        client_documents = documents_queryset.filter(document_type='Client')
        
        # Use the serializer to format the response
        serializer = StaffDocumentManagerSerializer({
            'company_documents': company_documents,
            'client_documents': client_documents,
            'year_filter': year_filter,
            'month_filter': month_filter
        })
        
        return Response(serializer.data, status=status.HTTP_200_OK)




